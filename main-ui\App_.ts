
import Vue from "vue";

interface AwardData {
    name: string;
    category: string;
    level: string;
    awardee: string;
    time: number;
    organization: string;
    projectType: string;
}

export default Vue.extend({
    name: "app",
    data() {
        return {
            isMobile: false,
            active: '/m/home',
        }
    },
    methods: {
        
    },
    computed: {
        rtKey(): string {
            return this.$route.name ?? "";
        },
    },
    mounted() {
        // 判断是否为移动设备
        const userAgent = navigator.userAgent;
        this.isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini|Mobile Safari|Windows Phone|Kindle|Silk|PlayBook/i.test(userAgent);
        // 如果为移动设备，则设置body的overflow为hidden
        if (this.isMobile) {
            document.body.style.overflow = 'hidden';
        }

        let data = "{\n \”name\“： \”二0一五年度广东省绿色建筑一等奖\“，\n \”category\“： \”省级\“，\n \”level\“： \”一等奖\“，\n \”获奖者\“： \”广东省建科建筑设计院\“，\n \”time\“： 1436966400000，\n \”organization\“： \”广东省工程勘察设计行业协会\“，\n \”projectType\“： \”检测实验大楼项目\“\n}\n'";
        try {
            // 方法3: 匹配 {...} 内容并修复中文标点符号
            const jsonMatch = data.match(/\{[^}]*\}/);
            if (jsonMatch) {
                let jsonContent = jsonMatch[0];

                // 修复中文标点符号和字段名
                let cleanData = jsonContent
                    .replace(/：/g, ':')           // 中文冒号改为英文冒号
                    .replace(/，/g, ',')           // 中文逗号改为英文逗号
                    .replace(/"/g, '"')           // 中文引号改为英文引号
                    .replace(/"/g, '"')           // 中文引号改为英文引号
                    .replace(/"获奖者"/g, '"awardee"')  // 修复字段名
                    .replace(/\\n/g, '')          // 去掉转义换行符
                    .replace(/\n/g, '')           // 去掉真实换行符
                    .replace(/\s+/g, ' ')         // 多个空白字符替换为单个空格
                    .trim();

                console.log('原始数据:', data);
                console.log('提取的JSON:', jsonContent);
                console.log('清理后的JSON:', cleanData);

                let obj: AwardData = JSON.parse(cleanData);
                console.log('解析成功的对象:', obj);
                console.log('对象属性 - 名称:', obj.name);
                console.log('对象属性 - 获奖者:', obj.awardee);

            } else {
                console.error('未找到有效的JSON格式');
            }

        } catch (error) {
            console.error('JSON解析错误:', error);
            console.log('原始数据:', data);
        }
    },
});