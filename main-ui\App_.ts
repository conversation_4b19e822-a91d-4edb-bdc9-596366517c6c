
import Vue from "vue";

interface AwardData {
    name: string;
    category: string;
    level: string;
    awardee: string;
    time: number;
    organization: string;
    projectType: string;
}

export default Vue.extend({
    name: "app",
    data() {
        return {
            isMobile: false,
            active: '/m/home',
        }
    },
    methods: {
        
    },
    computed: {
        rtKey(): string {
            return this.$route.name ?? "";
        },
    },
    mounted() {
        // 判断是否为移动设备
        const userAgent = navigator.userAgent;
        this.isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini|Mobile Safari|Windows Phone|Kindle|Silk|PlayBook/i.test(userAgent);
        // 如果为移动设备，则设置body的overflow为hidden
        if (this.isMobile) {
            document.body.style.overflow = 'hidden';
        }

        let data = "{\n  \"name\": \"二O一五年度广东省绿色建筑一等奖\",\n  \"category\": \"省级\",\n  \"level\": \"一等奖\",\n  \"awardee\": \"广东省建筑科学研究院集团股份有限公司\",\n  \"time\": 1437072000000,\n  \"organization\": \"广东省工程勘察设计行业协会\",\n  \"projectType\": \"检测实验大楼\"\n}\n`";

        const match = data.match(/\{[\s\S]*\}/);
if (match) {
    const jsonContent = match[0];
    console.log('提取的内容:', jsonContent);
    
    try {
        const obj = JSON.parse(jsonContent);
        console.log('解析成功:', obj);
    } catch (error) {
        console.error('解析失败:', error);
    }
} else {
    console.log('未找到大括号内容');
}
        // 截断最后的 \n'，只保留到 } 为止
        // const lastBraceIndex = data.lastIndexOf('}');
        // const cleanData = data.substring(0, lastBraceIndex + 1);

        // console.log('原始数据:', data);
        // console.log('截断后数据:', cleanData);

        // try {
        //     const obj = JSON.parse(cleanData);
        //     console.log('解析成功:', obj);
        // } catch (error) {
        //     console.error('解析失败:', error);
        // }
    },
});