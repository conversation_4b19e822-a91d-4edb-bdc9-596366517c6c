
import Vue from "vue";

interface AwardData {
    name: string;
    category: string;
    level: string;
    awardee: string;
    time: number;
    organization: string;
    projectType: string;
}

export default Vue.extend({
    name: "app",
    data() {
        return {
            isMobile: false,
            active: '/m/home',
        }
    },
    methods: {
        
    },
    computed: {
        rtKey(): string {
            return this.$route.name ?? "";
        },
    },
    mounted() {
        // 判断是否为移动设备
        const userAgent = navigator.userAgent;
        this.isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini|Mobile Safari|Windows Phone|Kindle|Silk|PlayBook/i.test(userAgent);
        // 如果为移动设备，则设置body的overflow为hidden
        if (this.isMobile) {
            document.body.style.overflow = 'hidden';
        }

        let data = "{\n \”name\“： \”二0一五年度广东省绿色建筑一等奖\“，\n \”category\“： \”省级\“，\n \”level\“： \”一等奖\“，\n \”获奖者\“： \”广东省建科建筑设计院\“，\n \”time\“： 1436966400000，\n \”organization\“： \”广东省工程勘察设计行业协会\“，\n \”projectType\“： \”检测实验大楼项目\“\n}\n'";
        try {
            // 方法1: 使用正则表达式清理 JSON 字符串
            let cleanData = data
                .replace(/\\n/g, '')           // 去掉 \n 换行符
                .replace(/\n/g, '')            // 去掉真实换行符
                .replace(/`/g, '')             // 去掉反引号
                .replace(/\s+/g, ' ')          // 将多个空白字符替换为单个空格
                .trim();                       // 去掉首尾空白

            let obj: AwardData = JSON.parse(cleanData);
            console.log('清理后的JSON字符串:', cleanData);
            console.log('解析后的对象:', obj);
            console.log('对象属性 - 名称:', obj.name);

            // 方法2: 更激进的清理（如果方法1还有问题）
            // let ultraCleanData = data.replace(/[`\n\r\t]/g, '').replace(/\s+/g, ' ').trim();
            // let obj2: AwardData = JSON.parse(ultraCleanData);

        } catch (error) {
            console.error('JSON解析错误:', error);
            console.log('原始数据:', data);
        }
    },
});