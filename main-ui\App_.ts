
import Vue from "vue";

interface AwardData {
    name: string;
    category: string;
    level: string;
    awardee: string;
    time: number;
    organization: string;
    projectType: string;
}

export default Vue.extend({
    name: "app",
    data() {
        return {
            isMobile: false,
            active: '/m/home',
        }
    },
    methods: {
        
    },
    computed: {
        rtKey(): string {
            return this.$route.name ?? "";
        },
    },
    mounted() {
        // 判断是否为移动设备
        const userAgent = navigator.userAgent;
        this.isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini|Mobile Safari|Windows Phone|Kindle|Silk|PlayBook/i.test(userAgent);
        // 如果为移动设备，则设置body的overflow为hidden
        if (this.isMobile) {
            document.body.style.overflow = 'hidden';
        }

        let data = "{\n  \"name\": \"二0一五年度广东省绿色建筑一等奖\",\n  \"category\": \"省级\",\n  \"level\": \"一等奖\",\n  \"awardee\": \"广东省建科建筑设计院\",\n  \"time\": 1436966400000,\n  \"organization\": \"广东省工程勘察设计行业协会\",\n  \"projectType\": \"检测实验大楼项目\"\n}\n`";
        try {
            let obj: AwardData = JSON.parse(data);
            console.log(obj);
        } catch (error) {
            
        }
    },
});