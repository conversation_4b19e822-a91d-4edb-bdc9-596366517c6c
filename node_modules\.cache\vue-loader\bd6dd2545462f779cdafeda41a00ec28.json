{"remainingRequest": "C:\\vue\\ysd-parse\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\vue\\ysd-parse\\ysd-compdb-ui\\src\\components\\AwardInfo.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\vue\\ysd-parse\\ysd-compdb-ui\\src\\components\\AwardInfo.vue", "mtime": 1755849039047}, {"path": "C:\\vue\\ysd-parse\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\vue\\ysd-parse\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\vue\\ysd-parse\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\vue\\ysd-parse\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\r\n    import a from \"./AwardInfo_\";\r\n    export default a;\r\n", {"version": 3, "sources": ["AwardInfo.vue"], "names": [], "mappings": ";AAgKA;AACA", "file": "AwardInfo.vue", "sourceRoot": "ysd-compdb-ui/src/components", "sourcesContent": ["<template>\r\n    <div class=\"award-info\" v-loading=\"vag_.loading\">\r\n        <div class=\"scroll-container\">\r\n            <div class=\"form-container\">\r\n            <!-- 奖项名称 -->\r\n            <div class=\"form-item\">\r\n                <label class=\"form-label\">奖项名称</label>\r\n                <el-input\r\n                    v-model=\"vag_.info.name\"\r\n                    placeholder=\"请输入内容\"\r\n                    class=\"form-input\"\r\n                />\r\n            </div>\r\n\r\n            <!-- 奖项级别 -->\r\n            <div class=\"form-item\">\r\n                <label class=\"form-label\">奖项级别</label>\r\n                <el-select\r\n                    v-model=\"vag_.info.category\"\r\n                    placeholder=\"请选择\"\r\n                    class=\"form-input\"\r\n                >\r\n                    <el-option\r\n                        v-for=\"item in vag_.categoryOptions\"\r\n                        :key=\"item.value\"\r\n                        :label=\"item.label\"\r\n                        :value=\"item.value\"\r\n                    />\r\n                </el-select>\r\n            </div>\r\n\r\n            <!-- 奖项等级 -->\r\n            <div class=\"form-item\">\r\n                <label class=\"form-label\">奖项等级</label>\r\n                <el-select\r\n                    v-model=\"vag_.info.level\"\r\n                    placeholder=\"请选择\"\r\n                    class=\"form-input\"\r\n                >\r\n                    <el-option\r\n                        v-for=\"item in vag_.levelOptions\"\r\n                        :key=\"item.value\"\r\n                        :label=\"item.label\"\r\n                        :value=\"item.value\"\r\n                    />\r\n                </el-select>\r\n            </div>\r\n\r\n            <!-- 获奖人 -->\r\n            <div class=\"form-item\">\r\n                <label class=\"form-label\">获奖人</label>\r\n                <el-input\r\n                    v-model=\"vag_.info.awardee\"\r\n                    placeholder=\"若为联合体，指设计方\"\r\n                    class=\"form-input\"\r\n                />\r\n            </div>\r\n\r\n            <!-- 获奖时间 -->\r\n            <div class=\"form-item\">\r\n                <label class=\"form-label\">获奖时间</label>\r\n                <div class=\"date-input\" @click=\"vag_.showDatePicker = true\">\r\n                    <i class=\"el-icon-date\"></i>\r\n                    <span v-if=\"vag_.displayTime\">{{ vag_.displayTime }}</span>\r\n                    <span v-else class=\"placeholder\">选择日期</span>\r\n                </div>\r\n            </div>\r\n\r\n            <!-- 颁奖单位 -->\r\n            <div class=\"form-item\">\r\n                <label class=\"form-label\">颁奖单位</label>\r\n                <el-input\r\n                    v-model=\"vag_.info.organization\"\r\n                    placeholder=\"请输入内容\"\r\n                    class=\"form-input\"\r\n                />\r\n            </div>\r\n\r\n            <!-- 获奖项目类型 -->\r\n            <div class=\"form-item\">\r\n                <label class=\"form-label\">获奖项目类型</label>\r\n                <el-select\r\n                    v-model=\"vag_.info.projectType\"\r\n                    placeholder=\"请选择\"\r\n                    class=\"form-input\"\r\n                >\r\n                    <el-option\r\n                        v-for=\"item in vag_.projectTypeOptions\"\r\n                        :key=\"item.value\"\r\n                        :label=\"item.label\"\r\n                        :value=\"item.value\"\r\n                    />\r\n                </el-select>\r\n            </div>\r\n\r\n            <!-- 证明材料 -->\r\n            <div class=\"form-item upload-section\">\r\n                <label class=\"form-label\">证明材料</label>\r\n                <div class=\"upload-area\">\r\n                    <div class=\"upload-tip\">\r\n                        请上传相关奖项证书扫描件（如盖章页位公章），系统可自动识别并输入相关信息\r\n                    </div>\r\n\r\n                    <!-- 文件上传区域 -->\r\n                    <div class=\"upload-zone\" @click=\"$refs.fileInput.click()\">\r\n                        <div class=\"upload-icon\">\r\n                            <i class=\"el-icon-plus\"></i>\r\n                        </div>\r\n                        <div class=\"upload-text\">\r\n                            <div class=\"upload-title\">上传</div>\r\n                            <div class=\"upload-subtitle\">识别相关字段，自动填入</div>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <!-- 隐藏的文件输入 -->\r\n                    <input\r\n                        ref=\"fileInput\"\r\n                        type=\"file\"\r\n                        multiple\r\n                        accept=\"image/*,.pdf\"\r\n                        style=\"display: none\"\r\n                        @change=\"vag_.handleFileUpload($event)\"\r\n                    />\r\n\r\n                    <!-- 已上传文件列表 -->\r\n                    <div v-if=\"vag_.documents.length > 0\" class=\"file-list\">\r\n                        <div\r\n                            v-for=\"(file, index) in vag_.documents\"\r\n                            :key=\"index\"\r\n                            class=\"file-item\"\r\n                        >\r\n                            <span class=\"file-name\">{{ file.name }}</span>\r\n                            <i class=\"el-icon-close\" @click=\"vag_.removeFile(index)\"></i>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <!-- 操作按钮 -->\r\n            <div class=\"form-actions\">\r\n                <el-button @click=\"vag_.handleCancel()\">取消</el-button>\r\n                <el-button type=\"primary\" @click=\"vag_.handleSubmit()\">确定</el-button>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- Vant 日期选择器 -->\r\n        <van-popup v-model=\"vag_.showDatePicker\" position=\"bottom\">\r\n            <van-datetime-picker\r\n                v-model=\"currentDate\"\r\n                type=\"date\"\r\n                title=\"选择日期\"\r\n                @confirm=\"vag_.onDateConfirm($event)\"\r\n                @cancel=\"vag_.onDateCancel()\"\r\n            />\r\n        </van-popup>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    import a from \"./AwardInfo_\";\r\n    export default a;\r\n</script>\r\n\r\n<style scoped>\r\n.award-info {\r\n    background-color: #f5f7fa;\r\n    height: 100vh;\r\n    overflow: hidden;\r\n    box-sizing: border-box;\r\n    display: flex;\r\n    flex-direction: column;\r\n}\r\n\r\n.scroll-container {\r\n    flex: 1;\r\n    overflow-y: auto;\r\n    padding: 20px;\r\n    box-sizing: border-box;\r\n    -webkit-overflow-scrolling: touch;\r\n}\r\n\r\n.form-container {\r\n    background-color: white;\r\n    border-radius: 8px;\r\n    padding: 20px;\r\n    max-width: 100%;\r\n    margin: 0 auto;\r\n    box-sizing: border-box;\r\n}\r\n\r\n.form-item {\r\n    margin-bottom: 20px;\r\n    width: 100%;\r\n    box-sizing: border-box;\r\n}\r\n\r\n.form-label {\r\n    display: block;\r\n    margin-bottom: 8px;\r\n    font-size: 14px;\r\n    color: #333;\r\n    font-weight: 500;\r\n}\r\n\r\n.form-input {\r\n    width: 100%;\r\n}\r\n\r\n.date-input {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 12px 15px;\r\n    border: 1px solid #dcdfe6;\r\n    border-radius: 4px;\r\n    background-color: white;\r\n    cursor: pointer;\r\n    min-height: 40px;\r\n    box-sizing: border-box;\r\n    width: 100%;\r\n    transition: border-color 0.3s;\r\n}\r\n\r\n.date-input:active {\r\n    border-color: #409eff;\r\n}\r\n\r\n.date-input i {\r\n    margin-right: 8px;\r\n    color: #c0c4cc;\r\n}\r\n\r\n.date-input .placeholder {\r\n    color: #c0c4cc;\r\n}\r\n\r\n.upload-section {\r\n    margin-bottom: 30px;\r\n}\r\n\r\n.upload-area {\r\n    border: 1px solid #dcdfe6;\r\n    border-radius: 4px;\r\n    padding: 20px;\r\n    background-color: #fafafa;\r\n    width: 100%;\r\n    box-sizing: border-box;\r\n}\r\n\r\n.upload-tip {\r\n    font-size: 12px;\r\n    color: #666;\r\n    margin-bottom: 15px;\r\n    line-height: 1.5;\r\n}\r\n\r\n.upload-zone {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    min-height: 80px;\r\n    border: 2px dashed #d9d9d9;\r\n    border-radius: 4px;\r\n    cursor: pointer;\r\n    transition: border-color 0.3s;\r\n}\r\n\r\n.upload-zone:hover {\r\n    border-color: #409eff;\r\n}\r\n\r\n.upload-icon {\r\n    margin-right: 15px;\r\n}\r\n\r\n.upload-icon i {\r\n    font-size: 24px;\r\n    color: #c0c4cc;\r\n}\r\n\r\n.upload-text {\r\n    text-align: center;\r\n}\r\n\r\n.upload-title {\r\n    font-size: 16px;\r\n    color: #333;\r\n    margin-bottom: 4px;\r\n}\r\n\r\n.upload-subtitle {\r\n    font-size: 12px;\r\n    color: #ff4d6d;\r\n}\r\n\r\n.file-list {\r\n    margin-top: 15px;\r\n}\r\n\r\n.file-item {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 8px 12px;\r\n    background-color: white;\r\n    border: 1px solid #e4e7ed;\r\n    border-radius: 4px;\r\n    margin-bottom: 8px;\r\n}\r\n\r\n.file-name {\r\n    font-size: 14px;\r\n    color: #333;\r\n    flex: 1;\r\n}\r\n\r\n.file-item i {\r\n    color: #c0c4cc;\r\n    cursor: pointer;\r\n    font-size: 16px;\r\n}\r\n\r\n.file-item i:hover {\r\n    color: #f56c6c;\r\n}\r\n\r\n.form-actions {\r\n    display: flex;\r\n    justify-content: center;\r\n    gap: 20px;\r\n    margin-top: 30px;\r\n    padding-top: 20px;\r\n    border-top: 1px solid #e4e7ed;\r\n}\r\n\r\n.form-actions .el-button {\r\n    min-width: 100px;\r\n}\r\n\r\n/* 移动端优化 */\r\n@media (max-width: 768px) {\r\n    .award-info {\r\n        height: 100vh;\r\n    }\r\n\r\n    .scroll-container {\r\n        padding: 16px;\r\n    }\r\n\r\n    .form-container {\r\n        padding: 16px;\r\n        border-radius: 0;\r\n        margin: 0;\r\n        box-sizing: border-box;\r\n    }\r\n\r\n    .form-item {\r\n        margin-bottom: 16px;\r\n    }\r\n\r\n    .form-label {\r\n        font-size: 14px;\r\n        margin-bottom: 6px;\r\n    }\r\n\r\n    .upload-area {\r\n        padding: 16px;\r\n    }\r\n\r\n    .upload-zone {\r\n        min-height: 60px;\r\n        flex-direction: column;\r\n        text-align: center;\r\n    }\r\n\r\n    .upload-icon {\r\n        margin-right: 0;\r\n        margin-bottom: 8px;\r\n    }\r\n\r\n    .form-actions {\r\n        position: sticky;\r\n        bottom: 0;\r\n        background-color: white;\r\n        padding: 16px 0;\r\n        margin-top: 20px;\r\n        border-top: 1px solid #e4e7ed;\r\n        z-index: 10;\r\n    }\r\n\r\n    .form-actions .el-button {\r\n        flex: 1;\r\n        margin: 0 8px;\r\n    }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n    .scroll-container {\r\n        padding: 12px;\r\n    }\r\n\r\n    .form-container {\r\n        padding: 12px;\r\n    }\r\n\r\n    .form-item {\r\n        margin-bottom: 12px;\r\n    }\r\n\r\n    .upload-tip {\r\n        font-size: 11px;\r\n    }\r\n\r\n    .upload-title {\r\n        font-size: 14px;\r\n    }\r\n\r\n    .upload-subtitle {\r\n        font-size: 11px;\r\n    }\r\n}\r\n</style>"]}]}