{"remainingRequest": "C:\\vue\\ysd-parse\\node_modules\\cache-loader\\dist\\cjs.js??ref--12-0!C:\\vue\\ysd-parse\\node_modules\\babel-loader\\lib\\index.js!C:\\vue\\ysd-parse\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!C:\\vue\\ysd-parse\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\vue\\ysd-parse\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\vue\\ysd-parse\\ysd-compdb-ui\\src\\components\\PerformanceList.vue?vue&type=template&id=66d9e52c&scoped=true", "dependencies": [{"path": "C:\\vue\\ysd-parse\\ysd-compdb-ui\\src\\components\\PerformanceList.vue", "mtime": 1755836423056}, {"path": "C:\\vue\\ysd-parse\\babel.config.js", "mtime": 1743389039755}, {"path": "C:\\vue\\ysd-parse\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\vue\\ysd-parse\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\vue\\ysd-parse\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\vue\\ysd-parse\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\vue\\ysd-parse\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\vue\\ysd-parse\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c,\n    _setup = _vm._self._setupProxy;\n  return _c(\"div\", {\n    staticClass: \"performance-list\"\n  }, [_c(\"div\", {\n    staticClass: \"action-bar\"\n  }, [_c(\"el-button\", {\n    attrs: {\n      type: \"primary\",\n      size: \"small\"\n    },\n    on: {\n      click: _vm.handleAdd\n    }\n  }, [_c(\"i\", {\n    staticClass: \"el-icon-plus\"\n  }), _vm._v(\" 添加\\n        \")]), _c(\"el-button\", {\n    attrs: {\n      size: \"small\"\n    },\n    on: {\n      click: _vm.handleBatchUpload\n    }\n  }, [_c(\"i\", {\n    staticClass: \"el-icon-upload\"\n  }), _vm._v(\" 批量上传\\n        \")])], 1), _c(\"el-table\", {\n    directives: [{\n      name: \"loading\",\n      rawName: \"v-loading\",\n      value: _vm.vag_.loading,\n      expression: \"vag_.loading\"\n    }],\n    staticClass: \"eltb eltb-scrl\",\n    attrs: {\n      height: \"0\",\n      data: _vm.vag_.performanceList,\n      border: \"\",\n      stripe: \"\"\n    }\n  }, [_c(\"el-table-column\", {\n    attrs: {\n      prop: \"projectName\",\n      label: \"项目名称\",\n      \"min-width\": \"200\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [_c(\"span\", {\n          staticClass: \"project-name\"\n        }, [_vm._v(_vm._s(scope.row.projectName))])];\n      }\n    }])\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"projectLeader\",\n      label: \"项目负责人\",\n      width: \"100\",\n      align: \"center\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [_c(\"span\", [_vm._v(_vm._s(scope.row.projectLeader))])];\n      }\n    }])\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"contractDate\",\n      label: \"合同签订日期\",\n      width: \"120\",\n      align: \"center\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [_c(\"span\", [_vm._v(_vm._s(scope.row.contractDate))])];\n      }\n    }])\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"contractAmount\",\n      label: \"建设规模（平方米）\",\n      width: \"148\",\n      align: \"right\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [_c(\"span\", {\n          staticClass: \"amount\"\n        }, [_vm._v(_vm._s(scope.row.contractAmount))])];\n      }\n    }])\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"designAmount\",\n      label: \"设计费（万元）\",\n      width: \"120\",\n      align: \"right\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [_c(\"span\", {\n          staticClass: \"amount\"\n        }, [_vm._v(_vm._s(scope.row.designAmount))])];\n      }\n    }])\n  }), _c(\"el-table-column\", {\n    attrs: {\n      prop: \"constructionAmount\",\n      label: \"建安工程费（万元）\",\n      width: \"148\",\n      align: \"right\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [_c(\"span\", {\n          staticClass: \"amount\"\n        }, [_vm._v(_vm._s(scope.row.constructionAmount))])];\n      }\n    }])\n  }), _c(\"el-table-column\", {\n    attrs: {\n      label: \"操作\",\n      width: \"180\",\n      align: \"center\",\n      fixed: \"right\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(scope) {\n        return [_c(\"el-button\", {\n          staticClass: \"action-btn edit-btn\",\n          attrs: {\n            type: \"text\",\n            size: \"small\"\n          },\n          on: {\n            click: function click($event) {\n              return _vm.vag_.handleEdit(scope.row);\n            }\n          }\n        }, [_vm._v(\"\\n                    编辑\\n                \")]), _c(\"el-button\", {\n          staticClass: \"action-btn delete-btn\",\n          attrs: {\n            type: \"text\",\n            size: \"small\"\n          },\n          on: {\n            click: function click($event) {\n              return _vm.vag_.handleDelete(scope.row);\n            }\n          }\n        }, [_vm._v(\"\\n                    删除\\n                \")])];\n      }\n    }])\n  })], 1)], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", {"version": 3, "names": ["render", "_vm", "_c", "_self", "_setup", "_setupProxy", "staticClass", "attrs", "type", "size", "on", "click", "handleAdd", "_v", "handleBatchUpload", "directives", "name", "rawName", "value", "vag_", "loading", "expression", "height", "data", "performanceList", "border", "stripe", "prop", "label", "scopedSlots", "_u", "key", "fn", "scope", "_s", "row", "projectName", "width", "align", "projectLeader", "contractDate", "contractAmount", "designAmount", "constructionAmount", "fixed", "$event", "handleEdit", "handleDelete", "staticRenderFns", "_withStripped"], "sources": ["C:/vue/ysd-parse/ysd-compdb-ui/src/components/PerformanceList.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c,\n    _setup = _vm._self._setupProxy\n  return _c(\n    \"div\",\n    { staticClass: \"performance-list\" },\n    [\n      _c(\n        \"div\",\n        { staticClass: \"action-bar\" },\n        [\n          _c(\n            \"el-button\",\n            {\n              attrs: { type: \"primary\", size: \"small\" },\n              on: { click: _vm.handleAdd },\n            },\n            [\n              _c(\"i\", { staticClass: \"el-icon-plus\" }),\n              _vm._v(\" 添加\\n        \"),\n            ]\n          ),\n          _c(\n            \"el-button\",\n            { attrs: { size: \"small\" }, on: { click: _vm.handleBatchUpload } },\n            [\n              _c(\"i\", { staticClass: \"el-icon-upload\" }),\n              _vm._v(\" 批量上传\\n        \"),\n            ]\n          ),\n        ],\n        1\n      ),\n      _c(\n        \"el-table\",\n        {\n          directives: [\n            {\n              name: \"loading\",\n              rawName: \"v-loading\",\n              value: _vm.vag_.loading,\n              expression: \"vag_.loading\",\n            },\n          ],\n          staticClass: \"eltb eltb-scrl\",\n          attrs: {\n            height: \"0\",\n            data: _vm.vag_.performanceList,\n            border: \"\",\n            stripe: \"\",\n          },\n        },\n        [\n          _c(\"el-table-column\", {\n            attrs: {\n              prop: \"projectName\",\n              label: \"项目名称\",\n              \"min-width\": \"200\",\n            },\n            scopedSlots: _vm._u([\n              {\n                key: \"default\",\n                fn: function (scope) {\n                  return [\n                    _c(\"span\", { staticClass: \"project-name\" }, [\n                      _vm._v(_vm._s(scope.row.projectName)),\n                    ]),\n                  ]\n                },\n              },\n            ]),\n          }),\n          _c(\"el-table-column\", {\n            attrs: {\n              prop: \"projectLeader\",\n              label: \"项目负责人\",\n              width: \"100\",\n              align: \"center\",\n            },\n            scopedSlots: _vm._u([\n              {\n                key: \"default\",\n                fn: function (scope) {\n                  return [_c(\"span\", [_vm._v(_vm._s(scope.row.projectLeader))])]\n                },\n              },\n            ]),\n          }),\n          _c(\"el-table-column\", {\n            attrs: {\n              prop: \"contractDate\",\n              label: \"合同签订日期\",\n              width: \"120\",\n              align: \"center\",\n            },\n            scopedSlots: _vm._u([\n              {\n                key: \"default\",\n                fn: function (scope) {\n                  return [_c(\"span\", [_vm._v(_vm._s(scope.row.contractDate))])]\n                },\n              },\n            ]),\n          }),\n          _c(\"el-table-column\", {\n            attrs: {\n              prop: \"contractAmount\",\n              label: \"建设规模（平方米）\",\n              width: \"148\",\n              align: \"right\",\n            },\n            scopedSlots: _vm._u([\n              {\n                key: \"default\",\n                fn: function (scope) {\n                  return [\n                    _c(\"span\", { staticClass: \"amount\" }, [\n                      _vm._v(_vm._s(scope.row.contractAmount)),\n                    ]),\n                  ]\n                },\n              },\n            ]),\n          }),\n          _c(\"el-table-column\", {\n            attrs: {\n              prop: \"designAmount\",\n              label: \"设计费（万元）\",\n              width: \"120\",\n              align: \"right\",\n            },\n            scopedSlots: _vm._u([\n              {\n                key: \"default\",\n                fn: function (scope) {\n                  return [\n                    _c(\"span\", { staticClass: \"amount\" }, [\n                      _vm._v(_vm._s(scope.row.designAmount)),\n                    ]),\n                  ]\n                },\n              },\n            ]),\n          }),\n          _c(\"el-table-column\", {\n            attrs: {\n              prop: \"constructionAmount\",\n              label: \"建安工程费（万元）\",\n              width: \"148\",\n              align: \"right\",\n            },\n            scopedSlots: _vm._u([\n              {\n                key: \"default\",\n                fn: function (scope) {\n                  return [\n                    _c(\"span\", { staticClass: \"amount\" }, [\n                      _vm._v(_vm._s(scope.row.constructionAmount)),\n                    ]),\n                  ]\n                },\n              },\n            ]),\n          }),\n          _c(\"el-table-column\", {\n            attrs: {\n              label: \"操作\",\n              width: \"180\",\n              align: \"center\",\n              fixed: \"right\",\n            },\n            scopedSlots: _vm._u([\n              {\n                key: \"default\",\n                fn: function (scope) {\n                  return [\n                    _c(\n                      \"el-button\",\n                      {\n                        staticClass: \"action-btn edit-btn\",\n                        attrs: { type: \"text\", size: \"small\" },\n                        on: {\n                          click: function ($event) {\n                            return _vm.vag_.handleEdit(scope.row)\n                          },\n                        },\n                      },\n                      [_vm._v(\"\\n                    编辑\\n                \")]\n                    ),\n                    _c(\n                      \"el-button\",\n                      {\n                        staticClass: \"action-btn delete-btn\",\n                        attrs: { type: \"text\", size: \"small\" },\n                        on: {\n                          click: function ($event) {\n                            return _vm.vag_.handleDelete(scope.row)\n                          },\n                        },\n                      },\n                      [_vm._v(\"\\n                    删除\\n                \")]\n                    ),\n                  ]\n                },\n              },\n            ]),\n          }),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;IACjBE,MAAM,GAAGH,GAAG,CAACE,KAAK,CAACE,WAAW;EAChC,OAAOH,EAAE,CACP,KAAK,EACL;IAAEI,WAAW,EAAE;EAAmB,CAAC,EACnC,CACEJ,EAAE,CACA,KAAK,EACL;IAAEI,WAAW,EAAE;EAAa,CAAC,EAC7B,CACEJ,EAAE,CACA,WAAW,EACX;IACEK,KAAK,EAAE;MAAEC,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC;IACzCC,EAAE,EAAE;MAAEC,KAAK,EAAEV,GAAG,CAACW;IAAU;EAC7B,CAAC,EACD,CACEV,EAAE,CAAC,GAAG,EAAE;IAAEI,WAAW,EAAE;EAAe,CAAC,CAAC,EACxCL,GAAG,CAACY,EAAE,CAAC,eAAe,CAAC,CAE3B,CAAC,EACDX,EAAE,CACA,WAAW,EACX;IAAEK,KAAK,EAAE;MAAEE,IAAI,EAAE;IAAQ,CAAC;IAAEC,EAAE,EAAE;MAAEC,KAAK,EAAEV,GAAG,CAACa;IAAkB;EAAE,CAAC,EAClE,CACEZ,EAAE,CAAC,GAAG,EAAE;IAAEI,WAAW,EAAE;EAAiB,CAAC,CAAC,EAC1CL,GAAG,CAACY,EAAE,CAAC,iBAAiB,CAAC,CAE7B,CAAC,CACF,EACD,CACF,CAAC,EACDX,EAAE,CACA,UAAU,EACV;IACEa,UAAU,EAAE,CACV;MACEC,IAAI,EAAE,SAAS;MACfC,OAAO,EAAE,WAAW;MACpBC,KAAK,EAAEjB,GAAG,CAACkB,IAAI,CAACC,OAAO;MACvBC,UAAU,EAAE;IACd,CAAC,CACF;IACDf,WAAW,EAAE,gBAAgB;IAC7BC,KAAK,EAAE;MACLe,MAAM,EAAE,GAAG;MACXC,IAAI,EAAEtB,GAAG,CAACkB,IAAI,CAACK,eAAe;MAC9BC,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE;IACV;EACF,CAAC,EACD,CACExB,EAAE,CAAC,iBAAiB,EAAE;IACpBK,KAAK,EAAE;MACLoB,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,MAAM;MACb,WAAW,EAAE;IACf,CAAC;IACDC,WAAW,EAAE5B,GAAG,CAAC6B,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACL/B,EAAE,CAAC,MAAM,EAAE;UAAEI,WAAW,EAAE;QAAe,CAAC,EAAE,CAC1CL,GAAG,CAACY,EAAE,CAACZ,GAAG,CAACiC,EAAE,CAACD,KAAK,CAACE,GAAG,CAACC,WAAW,CAAC,CAAC,CACtC,CAAC,CACH;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACFlC,EAAE,CAAC,iBAAiB,EAAE;IACpBK,KAAK,EAAE;MACLoB,IAAI,EAAE,eAAe;MACrBC,KAAK,EAAE,OAAO;MACdS,KAAK,EAAE,KAAK;MACZC,KAAK,EAAE;IACT,CAAC;IACDT,WAAW,EAAE5B,GAAG,CAAC6B,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CAAC/B,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACY,EAAE,CAACZ,GAAG,CAACiC,EAAE,CAACD,KAAK,CAACE,GAAG,CAACI,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;MAChE;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACFrC,EAAE,CAAC,iBAAiB,EAAE;IACpBK,KAAK,EAAE;MACLoB,IAAI,EAAE,cAAc;MACpBC,KAAK,EAAE,QAAQ;MACfS,KAAK,EAAE,KAAK;MACZC,KAAK,EAAE;IACT,CAAC;IACDT,WAAW,EAAE5B,GAAG,CAAC6B,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CAAC/B,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACY,EAAE,CAACZ,GAAG,CAACiC,EAAE,CAACD,KAAK,CAACE,GAAG,CAACK,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/D;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACFtC,EAAE,CAAC,iBAAiB,EAAE;IACpBK,KAAK,EAAE;MACLoB,IAAI,EAAE,gBAAgB;MACtBC,KAAK,EAAE,WAAW;MAClBS,KAAK,EAAE,KAAK;MACZC,KAAK,EAAE;IACT,CAAC;IACDT,WAAW,EAAE5B,GAAG,CAAC6B,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACL/B,EAAE,CAAC,MAAM,EAAE;UAAEI,WAAW,EAAE;QAAS,CAAC,EAAE,CACpCL,GAAG,CAACY,EAAE,CAACZ,GAAG,CAACiC,EAAE,CAACD,KAAK,CAACE,GAAG,CAACM,cAAc,CAAC,CAAC,CACzC,CAAC,CACH;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACFvC,EAAE,CAAC,iBAAiB,EAAE;IACpBK,KAAK,EAAE;MACLoB,IAAI,EAAE,cAAc;MACpBC,KAAK,EAAE,SAAS;MAChBS,KAAK,EAAE,KAAK;MACZC,KAAK,EAAE;IACT,CAAC;IACDT,WAAW,EAAE5B,GAAG,CAAC6B,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACL/B,EAAE,CAAC,MAAM,EAAE;UAAEI,WAAW,EAAE;QAAS,CAAC,EAAE,CACpCL,GAAG,CAACY,EAAE,CAACZ,GAAG,CAACiC,EAAE,CAACD,KAAK,CAACE,GAAG,CAACO,YAAY,CAAC,CAAC,CACvC,CAAC,CACH;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACFxC,EAAE,CAAC,iBAAiB,EAAE;IACpBK,KAAK,EAAE;MACLoB,IAAI,EAAE,oBAAoB;MAC1BC,KAAK,EAAE,WAAW;MAClBS,KAAK,EAAE,KAAK;MACZC,KAAK,EAAE;IACT,CAAC;IACDT,WAAW,EAAE5B,GAAG,CAAC6B,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACL/B,EAAE,CAAC,MAAM,EAAE;UAAEI,WAAW,EAAE;QAAS,CAAC,EAAE,CACpCL,GAAG,CAACY,EAAE,CAACZ,GAAG,CAACiC,EAAE,CAACD,KAAK,CAACE,GAAG,CAACQ,kBAAkB,CAAC,CAAC,CAC7C,CAAC,CACH;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACFzC,EAAE,CAAC,iBAAiB,EAAE;IACpBK,KAAK,EAAE;MACLqB,KAAK,EAAE,IAAI;MACXS,KAAK,EAAE,KAAK;MACZC,KAAK,EAAE,QAAQ;MACfM,KAAK,EAAE;IACT,CAAC;IACDf,WAAW,EAAE5B,GAAG,CAAC6B,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACL/B,EAAE,CACA,WAAW,EACX;UACEI,WAAW,EAAE,qBAAqB;UAClCC,KAAK,EAAE;YAAEC,IAAI,EAAE,MAAM;YAAEC,IAAI,EAAE;UAAQ,CAAC;UACtCC,EAAE,EAAE;YACFC,KAAK,EAAE,SAAPA,KAAKA,CAAYkC,MAAM,EAAE;cACvB,OAAO5C,GAAG,CAACkB,IAAI,CAAC2B,UAAU,CAACb,KAAK,CAACE,GAAG,CAAC;YACvC;UACF;QACF,CAAC,EACD,CAAClC,GAAG,CAACY,EAAE,CAAC,4CAA4C,CAAC,CACvD,CAAC,EACDX,EAAE,CACA,WAAW,EACX;UACEI,WAAW,EAAE,uBAAuB;UACpCC,KAAK,EAAE;YAAEC,IAAI,EAAE,MAAM;YAAEC,IAAI,EAAE;UAAQ,CAAC;UACtCC,EAAE,EAAE;YACFC,KAAK,EAAE,SAAPA,KAAKA,CAAYkC,MAAM,EAAE;cACvB,OAAO5C,GAAG,CAACkB,IAAI,CAAC4B,YAAY,CAACd,KAAK,CAACE,GAAG,CAAC;YACzC;UACF;QACF,CAAC,EACD,CAAClC,GAAG,CAACY,EAAE,CAAC,4CAA4C,CAAC,CACvD,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAImC,eAAe,GAAG,EAAE;AACxBhD,MAAM,CAACiD,aAAa,GAAG,IAAI;AAE3B,SAASjD,MAAM,EAAEgD,eAAe", "ignoreList": []}]}