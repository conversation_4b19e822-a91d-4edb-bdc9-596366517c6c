{"remainingRequest": "C:\\vue\\ysd-parse\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\vue\\ysd-parse\\ysd-compdb-ui\\src\\components\\AwardInfo.vue?vue&type=template&id=3024f2e9&scoped=true", "dependencies": [{"path": "C:\\vue\\ysd-parse\\ysd-compdb-ui\\src\\components\\AwardInfo.vue", "mtime": 1755849039047}, {"path": "C:\\vue\\ysd-parse\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\vue\\ysd-parse\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\vue\\ysd-parse\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\vue\\ysd-parse\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\vue\\ysd-parse\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\vue\\ysd-parse\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<div class=\"award-info\" v-loading=\"vag_.loading\">\n    <div class=\"scroll-container\">\n        <div class=\"form-container\">\n        <!-- 奖项名称 -->\n        <div class=\"form-item\">\n            <label class=\"form-label\">奖项名称</label>\n            <el-input\n                v-model=\"vag_.info.name\"\n                placeholder=\"请输入内容\"\n                class=\"form-input\"\n            />\n        </div>\n\n        <!-- 奖项级别 -->\n        <div class=\"form-item\">\n            <label class=\"form-label\">奖项级别</label>\n            <el-select\n                v-model=\"vag_.info.category\"\n                placeholder=\"请选择\"\n                class=\"form-input\"\n            >\n                <el-option\n                    v-for=\"item in vag_.categoryOptions\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                />\n            </el-select>\n        </div>\n\n        <!-- 奖项等级 -->\n        <div class=\"form-item\">\n            <label class=\"form-label\">奖项等级</label>\n            <el-select\n                v-model=\"vag_.info.level\"\n                placeholder=\"请选择\"\n                class=\"form-input\"\n            >\n                <el-option\n                    v-for=\"item in vag_.levelOptions\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                />\n            </el-select>\n        </div>\n\n        <!-- 获奖人 -->\n        <div class=\"form-item\">\n            <label class=\"form-label\">获奖人</label>\n            <el-input\n                v-model=\"vag_.info.awardee\"\n                placeholder=\"若为联合体，指设计方\"\n                class=\"form-input\"\n            />\n        </div>\n\n        <!-- 获奖时间 -->\n        <div class=\"form-item\">\n            <label class=\"form-label\">获奖时间</label>\n            <div class=\"date-input\" @click=\"vag_.showDatePicker = true\">\n                <i class=\"el-icon-date\"></i>\n                <span v-if=\"vag_.displayTime\">{{ vag_.displayTime }}</span>\n                <span v-else class=\"placeholder\">选择日期</span>\n            </div>\n        </div>\n\n        <!-- 颁奖单位 -->\n        <div class=\"form-item\">\n            <label class=\"form-label\">颁奖单位</label>\n            <el-input\n                v-model=\"vag_.info.organization\"\n                placeholder=\"请输入内容\"\n                class=\"form-input\"\n            />\n        </div>\n\n        <!-- 获奖项目类型 -->\n        <div class=\"form-item\">\n            <label class=\"form-label\">获奖项目类型</label>\n            <el-select\n                v-model=\"vag_.info.projectType\"\n                placeholder=\"请选择\"\n                class=\"form-input\"\n            >\n                <el-option\n                    v-for=\"item in vag_.projectTypeOptions\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                />\n            </el-select>\n        </div>\n\n        <!-- 证明材料 -->\n        <div class=\"form-item upload-section\">\n            <label class=\"form-label\">证明材料</label>\n            <div class=\"upload-area\">\n                <div class=\"upload-tip\">\n                    请上传相关奖项证书扫描件（如盖章页位公章），系统可自动识别并输入相关信息\n                </div>\n\n                <!-- 文件上传区域 -->\n                <div class=\"upload-zone\" @click=\"$refs.fileInput.click()\">\n                    <div class=\"upload-icon\">\n                        <i class=\"el-icon-plus\"></i>\n                    </div>\n                    <div class=\"upload-text\">\n                        <div class=\"upload-title\">上传</div>\n                        <div class=\"upload-subtitle\">识别相关字段，自动填入</div>\n                    </div>\n                </div>\n\n                <!-- 隐藏的文件输入 -->\n                <input\n                    ref=\"fileInput\"\n                    type=\"file\"\n                    multiple\n                    accept=\"image/*,.pdf\"\n                    style=\"display: none\"\n                    @change=\"vag_.handleFileUpload($event)\"\n                />\n\n                <!-- 已上传文件列表 -->\n                <div v-if=\"vag_.documents.length > 0\" class=\"file-list\">\n                    <div\n                        v-for=\"(file, index) in vag_.documents\"\n                        :key=\"index\"\n                        class=\"file-item\"\n                    >\n                        <span class=\"file-name\">{{ file.name }}</span>\n                        <i class=\"el-icon-close\" @click=\"vag_.removeFile(index)\"></i>\n                    </div>\n                </div>\n            </div>\n        </div>\n\n        <!-- 操作按钮 -->\n        <div class=\"form-actions\">\n            <el-button @click=\"vag_.handleCancel()\">取消</el-button>\n            <el-button type=\"primary\" @click=\"vag_.handleSubmit()\">确定</el-button>\n        </div>\n    </div>\n\n    <!-- Vant 日期选择器 -->\n    <van-popup v-model=\"vag_.showDatePicker\" position=\"bottom\">\n        <van-datetime-picker\n            v-model=\"currentDate\"\n            type=\"date\"\n            title=\"选择日期\"\n            @confirm=\"vag_.onDateConfirm($event)\"\n            @cancel=\"vag_.onDateCancel()\"\n        />\n    </van-popup>\n    </div>\n</div>\n", null]}