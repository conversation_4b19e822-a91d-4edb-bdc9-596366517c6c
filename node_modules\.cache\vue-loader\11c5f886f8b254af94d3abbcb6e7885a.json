{"remainingRequest": "C:\\vue\\ysd-parse\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\vue\\ysd-parse\\ysd-compdb-ui\\src\\components\\PerformanceList.vue?vue&type=style&index=1&id=66d9e52c&scoped=true&lang=css", "dependencies": [{"path": "C:\\vue\\ysd-parse\\ysd-compdb-ui\\src\\components\\PerformanceList.vue", "mtime": 1755836423056}, {"path": "C:\\vue\\ysd-parse\\node_modules\\css-loader\\index.js", "mtime": 499162500000}, {"path": "C:\\vue\\ysd-parse\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\vue\\ysd-parse\\node_modules\\postcss-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "C:\\vue\\ysd-parse\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\vue\\ysd-parse\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n.performance-list {\n    padding: 16px;\n    display: flex;\n    flex-direction: column;\n    flex-grow: 1;\n}\n\n.action-bar {\n    margin-bottom: 16px;\n    display: flex;\n    gap: 10px;\n}\n\n.project-name {\n    color: #333;\n    font-weight: 500;\n}\n\n.amount {\n    color: #409eff;\n    font-weight: 500;\n}\n\n.action-btn {\n    margin: 0 2px;\n    padding: 4px 8px;\n}\n\n.edit-btn {\n    color: #67c23a;\n}\n\n.delete-btn {\n    color: #f56c6c;\n}\n\n.action-btn:hover {\n    background-color: #f5f7fa;\n    border-radius: 4px;\n}\n\n.eltb {\n    flex-grow: 1;\n    overflow: overlay;\n}\n\n/* 表格样式优化 */\n.el-table {\n    border-radius: 8px;\n    overflow: hidden;\n}\n\n.el-table th {\n    background-color: #f8f9fa;\n    color: #333;\n    font-weight: 600;\n}\n\n.el-table td {\n    padding: 12px 0;\n}\n", {"version": 3, "sources": ["PerformanceList.vue"], "names": [], "mappings": ";AAwFA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "PerformanceList.vue", "sourceRoot": "ysd-compdb-ui/src/components", "sourcesContent": ["<template>\n    <!-- 业绩 -->\n    <div class=\"performance-list\">\n        <!-- 操作按钮区域 -->\n        <div class=\"action-bar\">\n            <el-button type=\"primary\" size=\"small\" @click=\"handleAdd\">\n                <i class=\"el-icon-plus\"></i> 添加\n            </el-button>\n            <el-button size=\"small\" @click=\"handleBatchUpload\">\n                <i class=\"el-icon-upload\"></i> 批量上传\n            </el-button>\n        </div>\n\n        <!-- 表格区域 -->\n        <el-table \n            class=\"eltb eltb-scrl\"\n            height=\"0\"\n            :data=\"vag_.performanceList\" \n            v-loading=\"vag_.loading\"\n            border\n            stripe\n        >\n            <el-table-column prop=\"projectName\" label=\"项目名称\" min-width=\"200\">\n                <template slot-scope=\"scope\">\n                    <span class=\"project-name\">{{ scope.row.projectName }}</span>\n                </template>\n            </el-table-column>\n            <!-- 项目负责人 -->\n             <el-table-column prop=\"projectLeader\" label=\"项目负责人\" width=\"100\" align=\"center\">\n                <template slot-scope=\"scope\">\n                    <span>{{ scope.row.projectLeader }}</span>\n                </template>\n            </el-table-column>\n            <el-table-column prop=\"contractDate\" label=\"合同签订日期\" width=\"120\" align=\"center\">\n                <template slot-scope=\"scope\">\n                    <span>{{ scope.row.contractDate }}</span>\n                </template>\n            </el-table-column>\n            \n            <el-table-column prop=\"contractAmount\" label=\"建设规模（平方米）\" width=\"148\" align=\"right\">\n                <template slot-scope=\"scope\">\n                    <span class=\"amount\">{{ scope.row.contractAmount }}</span>\n                </template>\n            </el-table-column>\n            \n            <el-table-column prop=\"designAmount\" label=\"设计费（万元）\" width=\"120\" align=\"right\">\n                <template slot-scope=\"scope\">\n                    <span class=\"amount\">{{ scope.row.designAmount }}</span>\n                </template>\n            </el-table-column>\n            \n            <el-table-column prop=\"constructionAmount\" label=\"建安工程费（万元）\" width=\"148\" align=\"right\">\n                <template slot-scope=\"scope\">\n                    <span class=\"amount\">{{ scope.row.constructionAmount }}</span>\n                </template>\n            </el-table-column>\n\n            <el-table-column label=\"操作\" width=\"180\" align=\"center\" fixed=\"right\">\n                <template slot-scope=\"scope\">\n                    <el-button \n                        type=\"text\" \n                        size=\"small\" \n                        @click=\"vag_.handleEdit(scope.row)\"\n                        class=\"action-btn edit-btn\"\n                    >\n                        编辑\n                    </el-button>\n                    <el-button \n                        type=\"text\" \n                        size=\"small\" \n                        @click=\"vag_.handleDelete(scope.row)\"\n                        class=\"action-btn delete-btn\"\n                    >\n                        删除\n                    </el-button>\n                </template>\n            </el-table-column>\n        </el-table>\n    </div>\n</template>\n\n<script>\nimport a from \"./PerformanceList_\";\nexport default a;\n</script>\n\n<style src=\"ysd-com-css/vue/eltb-scrl.css\" scoped></style>\n<style scoped>\n.performance-list {\n    padding: 16px;\n    display: flex;\n    flex-direction: column;\n    flex-grow: 1;\n}\n\n.action-bar {\n    margin-bottom: 16px;\n    display: flex;\n    gap: 10px;\n}\n\n.project-name {\n    color: #333;\n    font-weight: 500;\n}\n\n.amount {\n    color: #409eff;\n    font-weight: 500;\n}\n\n.action-btn {\n    margin: 0 2px;\n    padding: 4px 8px;\n}\n\n.edit-btn {\n    color: #67c23a;\n}\n\n.delete-btn {\n    color: #f56c6c;\n}\n\n.action-btn:hover {\n    background-color: #f5f7fa;\n    border-radius: 4px;\n}\n\n.eltb {\n    flex-grow: 1;\n    overflow: overlay;\n}\n\n/* 表格样式优化 */\n.el-table {\n    border-radius: 8px;\n    overflow: hidden;\n}\n\n.el-table th {\n    background-color: #f8f9fa;\n    color: #333;\n    font-weight: 600;\n}\n\n.el-table td {\n    padding: 12px 0;\n}\n</style>\n"]}]}