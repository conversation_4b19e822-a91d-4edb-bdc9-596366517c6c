{"remainingRequest": "C:\\vue\\ysd-parse\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\vue\\ysd-parse\\ysd-compdb-ui\\src\\components\\AwardInfo.vue", "dependencies": [{"path": "C:\\vue\\ysd-parse\\ysd-compdb-ui\\src\\components\\AwardInfo.vue", "mtime": 1755849039047}, {"path": "C:\\vue\\ysd-parse\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\vue\\ysd-parse\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./AwardInfo.vue?vue&type=template&id=3024f2e9&scoped=true\"\nimport script from \"./AwardInfo.vue?vue&type=script&lang=js\"\nexport * from \"./AwardInfo.vue?vue&type=script&lang=js\"\nimport style0 from \"./AwardInfo.vue?vue&type=style&index=0&id=3024f2e9&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3024f2e9\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"C:\\\\vue\\\\ysd-parse\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('3024f2e9')) {\n      api.createRecord('3024f2e9', component.options)\n    } else {\n      api.reload('3024f2e9', component.options)\n    }\n    module.hot.accept(\"./AwardInfo.vue?vue&type=template&id=3024f2e9&scoped=true\", function () {\n      api.rerender('3024f2e9', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"ysd-compdb-ui/src/components/AwardInfo.vue\"\nexport default component.exports"]}